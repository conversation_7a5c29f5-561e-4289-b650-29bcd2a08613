"use client";

import { useState, useEffect, useRef, useMemo, useCallback } from "react";
import InputSection from "./InputSection";
import TaskProgressSidebar from "./TaskProgressSidebar";
import ContentDisplayPanel from "./ContentDisplayPanel";
import VideoConfirmationDialog from "./VideoConfirmationDialog";
import { type Entries } from "@prisma/client";
import { useAuthUser } from "@/hooks/useAuthUser";

interface ScriptItem {
  title: string;
  description: string;
  code?: string;
}

interface Task {
  id: string;
  name: string;
  status: "pending" | "in-progress" | "completed" | "failed";
}

const BACKEND_URL =
  process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:8000";

export default function Bolt() {
  const { user } = useAuthUser();
  const abortControllerRef = useRef<AbortController | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [showGenerationUI, setShowGenerationUI] = useState(false);
  const [tasks, setTasks] = useState<Task[]>([]);
  const [currentScripts, setCurrentScripts] = useState<ScriptItem[]>([]);
  const [showConfirmationDialog, setShowConfirmationDialog] = useState(false);
  const [videoJobId, setVideoJobId] = useState<string | null>(null);
  const [, setQuizId] = useState<string | null>(null);

  const mainTheme = useMemo(() => {
    if (typeof window !== "undefined") {
      return localStorage.getItem("currentPrompt") || "";
    }
    return "";
  }, []);

  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  const generateScriptWithFetch = useCallback(async (prompt: string) => {
    try {
      setIsGenerating(true);

      // Create new abort controller for this request
      abortControllerRef.current = new AbortController();

      const response = await fetch("/api/generate-script", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ prompt }),
        signal: abortControllerRef.current.signal,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const generatedScripts: ScriptItem[] = await response.json();
      setCurrentScripts(generatedScripts);

      setTasks((prevTasks) =>
        prevTasks.map((task) =>
          task.id === "1"
            ? { ...task, status: "completed" }
            : task.id === "2"
            ? { ...task, status: "completed" }
            : task
        )
      );

      // Wait for quiz generation and get the quizId
      const generatedQuizId = await generateQuizWithFetch(
        generatedScripts.map((script) => script.title),
        generatedScripts.map((script) => script.description)
      );

      // Pass the quizId to generateManimCodes
      await generateManimCodes(generatedScripts, generatedQuizId);
    } catch (error) {
      if (error instanceof Error && error.name === "AbortError") {
        console.log("Request was aborted");
        return;
      }

      setTasks((prevTasks) =>
        prevTasks.map((task) =>
          task.status === "in-progress"
            ? {
                ...task,
                status: "failed",
                name: `${task.name} (Error: ${
                  error instanceof Error ? error.message : String(error)
                })`,
              }
            : task
        )
      );
    }
  }, []);

  const generateQuizWithFetch = async (
    title: string[],
    description: string[]
  ): Promise<string | null> => {
    try {
      const combinedTitle = title.map((t) => t.trim()).join(",");
      const combinedDescription = description.map((d) => d.trim()).join("\n");
      const userId = user.id;

      const response = await fetch("/api/ai-quiz", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          title: combinedTitle,
          content: combinedDescription,
          userId: userId,
        }),
        signal: abortControllerRef.current?.signal,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const quizData = (await response.json()) as Entries;
      setQuizId(quizData.id);
      return quizData.id; // Return the quizId
    } catch (error) {
      if (error instanceof Error && error.name === "AbortError") {
        return null;
      }

      console.error("Error generating quiz:", error);
      return null;
    }
  };

  const generateManimCodeWithFetch = useCallback(
    async (title: string, description: string): Promise<string | null> => {
      try {
        const response = await fetch("/api/manim", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            schema: { title, description },
            mainTheme: mainTheme,
          }),
          signal: abortControllerRef.current?.signal,
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        const code = data.code;

        setCurrentScripts((prev) =>
          prev.map((script) =>
            script.title === title ? { ...script, code: code } : script
          )
        );

        setTasks((prevTasks) =>
          prevTasks.map((task) =>
            task.id === `manim-${title}`
              ? { ...task, status: "completed" }
              : task
          )
        );

        return code;
      } catch (error) {
        if (error instanceof Error && error.name === "AbortError") {
          return null;
        }

        setTasks((prevTasks) =>
          prevTasks.map((task) =>
            task.id === `manim-${title}`
              ? {
                  ...task,
                  status: "failed",
                  name: `${task.name} (Error: ${
                    error instanceof Error ? error.message : String(error)
                  })`,
                }
              : task
          )
        );
        return null;
      }
    },
    [mainTheme]
  );

  const renderVideoWithBackend = useCallback(
    async (scripts: ScriptItem[], quizId: string) => {
      try {
        console.log("Rendering video with backend");

        const manimCodes = scripts
          .filter((script) => script.code)
          .map((script) => script.code!);

        if (manimCodes.length === 0) {
          throw new Error("No valid scripts to render");
        }

        if (!quizId) {
          throw new Error("Quiz ID not available");
        }

        // Comment out backend call for now and use dummy data
        /*
        const renderJobData = manimCodes.map((code, index) => ({
          script: code,
          scene_name: scripts[index]?.title || null,
          entry_id: null,
        }));

        const renderPayload = {
          scripts: renderJobData,
          priority: 0,
          entry_id: quizId,
        };

        const response = await fetch(`${BACKEND_URL}/batch_render`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(renderPayload),
          signal: abortControllerRef.current?.signal,
        });

        if (!response.ok) {
          throw new Error(`Backend error: ${response.status}`);
        }

        const result = await response.json();
        setVideoJobId(result.job_id);
        */

        // Use dummy video job ID
        const dummyJobId = `video_${Date.now()}_${Math.random()
          .toString(36)
          .substr(2, 9)}`;
        setVideoJobId(dummyJobId);

        setTasks((prevTasks) => [
          ...prevTasks,
          {
            id: "4",
            name: "Rendering Video",
            status: "completed",
          },
        ]);

        // Show confirmation dialog after a short delay
        setTimeout(() => {
          setIsGenerating(false); // Generation is complete
          setShowConfirmationDialog(true);
        }, 500);
      } catch (error) {
        if (error instanceof Error && error.name === "AbortError") {
          return;
        }

        console.error("Error rendering video:", error);
        setTasks((prevTasks) => [
          ...prevTasks,
          {
            id: "4",
            name: `Video Rendering Failed: ${
              error instanceof Error ? error.message : String(error)
            }`,
            status: "failed",
          },
        ]);
      }
    },
    []
  );

  const generateManimCodes = useCallback(
    async (scripts: ScriptItem[], quizId: string | null) => {
      const manimTasks: Task[] = scripts.map((script) => ({
        id: `manim-${script.title}`,
        name: `Generating: ${script.title}`,
        status: "pending",
      }));

      setTasks((prevTasks) => [
        ...prevTasks.filter((t) => t.id !== "3"),
        ...manimTasks,
        { id: "3", name: "Generating Animations", status: "in-progress" },
      ]);

      const scriptsWithCode: ScriptItem[] = [];

      for (const scriptItem of scripts) {
        setTasks((prevTasks) =>
          prevTasks.map((task) =>
            task.id === `manim-${scriptItem.title}`
              ? { ...task, status: "in-progress" }
              : task
          )
        );

        const code = await generateManimCodeWithFetch(
          scriptItem.title,
          scriptItem.description
        );

        if (code) {
          scriptsWithCode.push({ ...scriptItem, code });
        }
      }

      setTasks((prevTasks) =>
        prevTasks.map((task) =>
          task.id === "3" ? { ...task, status: "completed" } : task
        )
      );

      // Show video confirmation after animation generation is complete
      if (scriptsWithCode.length > 0 && quizId) {
        setTimeout(() => {
          renderVideoWithBackend(scriptsWithCode, quizId);
        }, 1000);
      } else {
        console.warn("No scripts with code generated or quiz ID missing");
      }
    },
    [generateManimCodeWithFetch, renderVideoWithBackend]
  );

  const handleGenerateClick = useCallback(
    async (prompt: string) => {
      setIsGenerating(true);
      setShowGenerationUI(true);
      setCurrentScripts([]);
      setQuizId(null);

      // Store the current prompt for later use
      if (typeof window !== "undefined") {
        localStorage.setItem("currentPrompt", prompt);
      }

      const initialTasks: Task[] = [
        { id: "1", name: "Analyzing Input", status: "in-progress" },
        { id: "2", name: "Generating Script", status: "pending" },
        { id: "3", name: "Generating Animations", status: "pending" },
      ];
      setTasks(initialTasks);

      try {
        setTasks((prevTasks) =>
          prevTasks.map((task) =>
            task.id === "2" ? { ...task, status: "in-progress" } : task
          )
        );
        await generateScriptWithFetch(prompt);
      } catch (error) {
        if (error instanceof Error && error.name === "AbortError") {
          console.log("Generation was aborted");
          return;
        }

        setTasks((prevTasks) =>
          prevTasks.map((task) =>
            task.status === "in-progress"
              ? {
                  ...task,
                  status: "failed",
                  name: `${task.name} (Error: ${
                    error instanceof Error ? error.message : String(error)
                  })`,
                }
              : task
          )
        );
      } finally {
        // Don't set isGenerating to false here to keep UI visible
        // setIsGenerating(false);
      }
    },
    [generateScriptWithFetch]
  );

  const handleClose = useCallback(() => {
    // Abort any ongoing requests
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    // Don't immediately set isGenerating to false to prevent abrupt UI change
    // setIsGenerating(false);
  }, []);

  const handleDialogClose = useCallback(() => {
    setShowConfirmationDialog(false);
  }, []);

  const handleResetToInput = useCallback(() => {
    setShowConfirmationDialog(false);
    // Reset to input screen after dialog is closed
    setTimeout(() => {
      setIsGenerating(false);
      setShowGenerationUI(false);
      setCurrentScripts([]);
      setTasks([]);
      setVideoJobId(null);
      setQuizId(null);
    }, 300); // Delay to allow smooth dialog close animation
  }, []);

  return (
    <>
      <InputSection
        key={"input-section"}
        isGenerating={isGenerating}
        onGenerate={handleGenerateClick}
      />

      {showGenerationUI && (
        <div className="flex h-screen w-full bg-background text-foreground overflow-hidden">
          <TaskProgressSidebar isGenerating={isGenerating} tasks={tasks} />

          <ContentDisplayPanel
            isGenerating={isGenerating}
            currentScripts={currentScripts}
            onClose={handleClose}
          />
        </div>
      )}

      <VideoConfirmationDialog
        key={"video-confirmation-dialog"}
        isOpen={showConfirmationDialog}
        onClose={handleDialogClose}
        onContinueWorking={handleResetToInput}
        videoJobId={videoJobId}
      />
    </>
  );
}
