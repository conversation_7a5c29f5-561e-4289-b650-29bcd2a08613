"use client";

import { AnimatePresence, motion } from "framer-motion";
import { Accordion } from "@/components/ui/accordion";
import { CheckCircle2, Clock, AlertCircle, Loader } from "lucide-react";

interface Task {
  id: string;
  name: string;
  status: "pending" | "in-progress" | "completed" | "failed";
}

interface ScriptItem {
  title: string;
  description: string;
  code?: string;
}

interface TaskProgressSidebarProps {
  isGenerating: boolean;
  tasks: Task[];
  scriptHistory?: ScriptItem[][];
  currentGeneratingScene?: string | null;
}

export default function TaskProgressSidebar({
  isGenerating,
  tasks,
  scriptHistory = [],
  currentGeneratingScene = null,
}: TaskProgressSidebarProps) {
  const getStatusIcon = (status: Task["status"]) => {
    switch (status) {
      case "completed":
        return <CheckCircle2 className="h-4 w-4 text-green-500" />;
      case "in-progress":
        return <Loader className="h-4 w-4 text-blue-500 animate-spin" />;
      case "pending":
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case "failed":
        return <AlertCircle className="h-4 w-4 text-destructive" />;
      default:
        return <Clock className="h-4 w-4 text-muted-foreground" />;
    }
  };

  const getStatusColor = (status: Task["status"]) => {
    switch (status) {
      case "completed":
        return "text-green-500";
      case "in-progress":
        return "text-blue-500";
      case "pending":
        return "text-yellow-500";
      case "failed":
        return "text-destructive";
      default:
        return "text-muted-foreground";
    }
  };

  if (!isGenerating) return null;

  return (
    <AnimatePresence>
      <motion.div
        className="w-full md:w-[30%] min-w-[300px] bg-gradient-to-b from-background to-secondary/20 border-r border-border/50 flex flex-col relative backdrop-blur-xl"
        animate={{
          width: isGenerating ? "30%" : "100%",
        }}
        transition={{ duration: 0.7, ease: [0.32, 0.72, 0, 1] }}
      >
        <div className="p-6 border-b border-border/50 bg-background/80 backdrop-blur-sm">
          <div className="flex items-center">Clarif.AI</div>
        </div>

        <div className="flex-1 overflow-auto">
          <div className="p-6">
            {/* Task List */}
            <div className="mb-8">
              <div className="flex items-center mb-4">
                <div className="w-1 h-6 bg-gradient-to-b from-primary to-primary/50 rounded-full mr-3"></div>
                <h3 className="text-lg font-semibold">Progress</h3>
              </div>
              <div className="space-y-3">
                {tasks.map((task) => (
                  <div
                    key={task.id}
                    className={`group flex items-center p-4 rounded-xl border transition-all duration-200 ${
                      task.status === "in-progress"
                        ? "bg-gradient-to-r from-primary/10 to-secondary/10 border-primary/20 shadow-lg shadow-primary/5"
                        : task.status === "completed"
                        ? "bg-gradient-to-r from-green-500/10 to-emerald-500/5 border-green-500/20"
                        : task.status === "failed"
                        ? "bg-gradient-to-r from-destructive/10 to-red-500/10 border-destructive/20"
                        : "bg-secondary/30 border-border/50 hover:bg-secondary/50"
                    }`}
                  >
                    <div className="flex items-center space-x-3 flex-1">
                      <div
                        className={`p-2 rounded-lg ${
                          task.status === "in-progress"
                            ? "bg-primary/20"
                            : task.status === "completed"
                            ? "bg-green-500/20"
                            : task.status === "failed"
                            ? "bg-destructive/20"
                            : "bg-secondary/50"
                        }`}
                      >
                        {getStatusIcon(task.status)}
                      </div>
                      <div className="flex-1">
                        <h4 className="font-medium text-sm">{task.name}</h4>
                        {task.status === "in-progress" &&
                          currentGeneratingScene && (
                            <p className="text-xs text-muted-foreground mt-1">
                              Currently: {currentGeneratingScene}
                            </p>
                          )}
                      </div>
                      <div
                        className={`text-xs font-medium px-2 py-1 rounded-full ${
                          task.status === "completed"
                            ? "bg-green-500/20 text-green-500"
                            : task.status === "in-progress"
                            ? "bg-blue-500/20 text-blue-500"
                            : task.status === "failed"
                            ? "bg-destructive/20 text-destructive"
                            : "bg-secondary text-muted-foreground"
                        }`}
                      >
                        {task.status}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* History Section */}
            {scriptHistory.length > 0 && (
              <div>
                <div className="flex items-center mb-4">
                  <div className="w-1 h-6 bg-gradient-to-b from-secondary to-muted rounded-full mr-3"></div>
                  <h3 className="text-lg font-semibold">History</h3>
                </div>
                <Accordion
                  type="single"
                  collapsible
                  className="w-full space-y-3"
                >
                  {scriptHistory.map((scripts, index) => (
                    <div key={index} className="border rounded-lg p-2">
                      <div className="text-sm font-medium">
                        Session {index + 1}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {scripts.length} scripts generated
                      </div>
                    </div>
                  ))}
                </Accordion>
              </div>
            )}
          </div>
        </div>

        <div className="p-6 border-t border-border/50 bg-background/80 backdrop-blur-sm">
          <div className="flex justify-between items-center">
            <div className="text-xs text-muted-foreground font-medium">
              <span className="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                {tasks.filter((t) => t.status === "completed").length}
              </span>
              <span className="mx-1">/</span>
              <span>{tasks.length}</span>
              <span className="ml-1">tasks completed</span>
            </div>
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
}
