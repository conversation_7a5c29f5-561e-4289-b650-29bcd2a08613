"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import {
  <PERSON><PERSON>les,
  Loader,
  FileUp,
  Terminal,
  Monitor as MonitorIcon,
  Figma,
} from "lucide-react";

const EXAMPLE_ACTIONS = [
  { icon: <Figma className="h-4 w-4" />, text: "Bayesian Theorem in ML" },
  { icon: <FileUp className="h-4 w-4" />, text: "Hidden Markov Models (HMM)" },
  {
    icon: <MonitorIcon className="h-4 w-4" />,
    text: "Gaussian Mixture Models",
  },
  { icon: <Terminal className="h-4 w-4" />, text: "Linked Lists in DSA" },
  { icon: <FileUp className="h-4 w-4" />, text: "Binary Trees in DSA" },
  { icon: <Figma className="h-4 w-4" />, text: "Quadratic Equations in Maths" },
  {
    icon: <FileUp className="h-4 w-4" />,
    text: "Projectile Motion in Physics",
  },
  {
    icon: <MonitorIcon className="h-4 w-4" />,
    text: "Dynamic Programming in DSA",
  },
  {
    icon: <Terminal className="h-4 w-4" />,
    text: "Eigenvalues and Eigenvectors",
  },
  { icon: <FileUp className="h-4 w-4" />, text: "Fourier Transform in Maths" },
  { icon: <Figma className="h-4 w-4" />, text: "Convex Optimization in Maths" },
  { icon: <MonitorIcon className="h-4 w-4" />, text: "Graph Theory in DSA" },
  { icon: <Terminal className="h-4 w-4" />, text: "Quantum Mechanics Basics" },
  { icon: <FileUp className="h-4 w-4" />, text: "Neural Networks in ML" },
];

interface InputSectionProps {
  isGenerating: boolean;
  onGenerate: (prompt: string) => void;
}

export default function InputSection({
  isGenerating,
  onGenerate,
}: InputSectionProps) {
  const [inputValue, setInputValue] = useState("");
  const [isEnhancing, setIsEnhancing] = useState(false);

  const enhancePrompt = async (prompt: string) => {
    if (!prompt.trim()) {
      setInputValue("");
      return;
    }

    try {
      setIsEnhancing(true);
      localStorage.setItem("currentPrompt", prompt.trim());

      const response = await fetch("/api/enhance", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ prompt: prompt.trim() }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const enhancedPrompt = await response.text();
      setInputValue(enhancedPrompt);
    } catch (error) {
      console.error("Error enhancing prompt:", error);
    } finally {
      setIsEnhancing(false);
    }
  };

  const handleGenerateClick = () => {
    if (!inputValue.trim()) return;
    localStorage.setItem(
      "currentPrompt",
      inputValue.trim().substring(0, 160) + "..."
    );
    onGenerate(inputValue);
  };

  return (
    <AnimatePresence>
      {!isGenerating && (
        <motion.div
          key="idle-ui"
          className="selection-accent flex flex-grow flex-col py-20 w-full items-center justify-center relative"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.4 }}
        >
          <div className="flex w-1/2 h-24 rounded-full bg-primary/20 blur-3xl absolute -top-10 left-1/2 -translate-x-1/2 text-foreground overflow-hidden" />
          <div className="mx-4 flex flex-col items-center">
            <div className="mb-12 text-center">
              <h1 className="mb-6 text-5xl md:text-6xl font-medium tracking-tight text-transparent bg-clip-text bg-gradient-to-br from-foreground to-muted/70 via-foreground/80">
                What do you want to learn?
              </h1>
              <p className="text-lg text-muted-foreground max-w-md mx-auto">
                Create animated explanations for any complex topic in minutes.
                For both{" "}
                <span className="font-medium text-foreground">students</span>{" "}
                and{" "}
                <span className="font-medium text-foreground">teachers</span> .
              </p>
            </div>

            <div className="mx-auto mb-6 w-full max-w-xl">
              <div className="shadow-xl dark:shadow-primary/20 dark:shadow-2xl relative rounded-lg">
                <div className="flex flex-col rounded-lg border bg-gradient-to-b from-secondary/40 to-background p-3 pb-6 relative overflow-hidden">
                  <div className="absolute bottom-0 left-0 w-full h-px bg-gradient-to-r from-transparent to-transparent via-primary pointer-events-none select-none"></div>
                  <div className="absolute bottom-0 left-0 w-full h-3 bg-gradient-to-r from-transparent to-transparent via-primary pointer-events-none select-none blur-2xl"></div>
                  <textarea
                    placeholder="Explain bayes theorem in machine learning"
                    className="h-32 w-full outline-none resize-none text-sm"
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                  />
                  <div className="mt-auto flex gap-2 absolute bottom-2 right-2 ">
                    <Button
                      variant="ghost"
                      size="sm"
                      className={cn(
                        "backdrop-blur-lg shadow",
                        isEnhancing ? "animate-pulse" : null
                      )}
                      disabled={
                        !inputValue.trim() ||
                        isEnhancing ||
                        inputValue.length < 6 ||
                        inputValue.length > 300
                      }
                      onClick={() => {
                        if (!inputValue.trim()) return;
                        enhancePrompt(inputValue.trim());
                      }}
                    >
                      {isEnhancing ? (
                        <Loader className="animate-spin size-4" />
                      ) : (
                        <Sparkles className="size-4" />
                      )}
                      {isEnhancing ? "Enhancing..." : "Enhance"}
                    </Button>
                    <Button
                      size="sm"
                      onClick={handleGenerateClick}
                      disabled={!inputValue.trim() || isEnhancing}
                    >
                      Generate
                    </Button>
                  </div>
                </div>
              </div>
            </div>

            <div className="mx-auto mt-16 flex w-full max-w-6xl flex-wrap justify-center gap-2">
              {EXAMPLE_ACTIONS.map((action, index) => (
                <Button
                  key={index}
                  size="sm"
                  variant="outline"
                  className="rounded-full px-4 py-0.5 text-xs"
                  onClick={() => setInputValue(action.text)}
                >
                  {action.icon}
                  <span>{action.text}</span>
                </Button>
              ))}
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
