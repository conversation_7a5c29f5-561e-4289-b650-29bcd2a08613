"use client";

import { motion, AnimatePresence } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Copy, FileText, Play } from "lucide-react";

interface ScriptItem {
  title: string;
  description: string;
  code?: string;
}

interface ContentDisplayPanelProps {
  isGenerating: boolean;
  currentScripts: ScriptItem[];
  manimStreams?: Record<string, string>;
  onClose: () => void;
}

export default function ContentDisplayPanel({
  isGenerating,
  currentScripts,
  manimStreams = {},
  onClose,
}: ContentDisplayPanelProps) {
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  if (!isGenerating) return null;

  return (
    <AnimatePresence>
      <motion.div
        className="bg-gradient-to-br from-background to-secondary/10 flex-1 flex flex-col absolute top-0 bottom-0 right-0 h-full backdrop-blur-xl border-l border-border/50"
        initial={{ width: "0%", x: "100%" }}
        animate={{ width: "70%", x: "0%" }}
        exit={{ width: "0%", x: "100%" }}
        transition={{ duration: 0.7, ease: [0.32, 0.72, 0, 1] }}
      >
        <div className="flex items-center justify-between px-6 py-4 border-b border-border/50 bg-background/80 backdrop-blur-sm">
          <div className="flex items-center space-x-3">
            <div className="w-2 h-2 rounded-full bg-gradient-to-r from-primary to-secondary animate-pulse"></div>
            <div className="text-sm font-medium">Generated Content</div>
          </div>
          <Button
            size="sm"
            variant="ghost"
            onClick={onClose}
            className="text-muted-foreground hover:text-foreground hover:bg-secondary/50 transition-all duration-200 rounded-lg"
          >
            Close
          </Button>
        </div>

        <Tabs defaultValue="script" className="flex-1 flex flex-col">
          <TabsList>
            <TabsTrigger value="script">Script</TabsTrigger>
            <TabsTrigger value="preview">Animation Preview</TabsTrigger>
          </TabsList>

          <TabsContent value="script" className="flex-1 overflow-hidden">
            <ScrollArea className="h-full p-6">
              <div className="space-y-6">
                {currentScripts.map((script, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <Card className="bg-gradient-to-br from-background to-secondary/5 border border-border/50 hover:border-primary/20 transition-all duration-200">
                      <CardHeader className="pb-3">
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-lg font-semibold bg-gradient-to-r from-foreground to-muted-foreground bg-clip-text text-transparent">
                            {script.title}
                          </CardTitle>
                          <Badge variant="secondary" className="text-xs">
                            Scene {index + 1}
                          </Badge>
                        </div>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div>
                          <h4 className="text-sm font-medium mb-2 flex items-center">
                            <FileText className="w-4 h-4 mr-2 text-primary" />
                            Description
                          </h4>
                          <p className="text-sm text-muted-foreground leading-relaxed bg-secondary/20 rounded-lg p-3 border border-border/30">
                            {script.description}
                          </p>
                        </div>

                        {script.code && (
                          <div>
                            <div className="flex items-center justify-between mb-2">
                              <h4 className="text-sm font-medium flex items-center">
                                <Play className="w-4 h-4 mr-2 text-green-500" />
                                Animation Code
                              </h4>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => copyToClipboard(script.code!)}
                                className="h-6 px-2 text-xs"
                              >
                                <Copy className="w-3 h-3 mr-1" />
                                Copy
                              </Button>
                            </div>
                            <div className="bg-slate-950 rounded-lg border border-border/30 overflow-hidden">
                              <pre className="p-4 text-xs overflow-x-auto">
                                <code className="text-green-400 font-mono">
                                  {script.code}
                                </code>
                              </pre>
                            </div>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}

                {currentScripts.length === 0 && (
                  <div className="flex items-center justify-center h-64">
                    <div className="text-center space-y-3">
                      <div className="w-12 h-12 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-full flex items-center justify-center mx-auto">
                        <FileText className="w-6 h-6 text-primary" />
                      </div>
                      <p className="text-muted-foreground">
                        Scripts will appear here as they're generated...
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </ScrollArea>
          </TabsContent>

          <TabsContent
            value="preview"
            className="flex-1 overflow-hidden bg-gradient-to-br from-slate-950 to-gray-900"
          >
            <ScrollArea className="h-full">
              <div className="p-6">
                <div className="grid gap-6">
                  {Object.entries(manimStreams).map(([title, code], index) => (
                    <motion.div
                      key={title}
                      initial={{ opacity: 0, scale: 0.95 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: index * 0.1 }}
                      className="bg-gradient-to-br from-slate-900 to-slate-800 rounded-xl border border-slate-700/50 p-6"
                    >
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-semibold text-white">
                          {title}
                        </h3>
                        <Badge
                          variant="outline"
                          className="text-green-400 border-green-400/50"
                        >
                          Ready to Render
                        </Badge>
                      </div>
                      <div className="bg-black/50 rounded-lg p-4 border border-slate-600/30">
                        <pre className="text-xs text-green-300 font-mono overflow-x-auto">
                          <code>{code}</code>
                        </pre>
                      </div>
                    </motion.div>
                  ))}

                  {Object.keys(manimStreams).length === 0 && (
                    <div className="flex items-center justify-center h-64">
                      <div className="text-center space-y-3">
                        <div className="w-12 h-12 bg-gradient-to-br from-green-500/20 to-emerald-500/20 rounded-full flex items-center justify-center mx-auto">
                          <Play className="w-6 h-6 text-green-400" />
                        </div>
                        <p className="text-slate-400">
                          Animation previews will appear here...
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </ScrollArea>
          </TabsContent>
        </Tabs>
      </motion.div>
    </AnimatePresence>
  );
}
