import { generateText } from "ai";
import { google } from "@ai-sdk/google";
import { NextResponse } from "next/server";
import { groq } from "@ai-sdk/groq";

export const maxDuration = 40;

export async function POST(req: Request) {
  const {
    schema,
    mainTheme = "",
  }: {
    schema: { title: string; description: string };
    mainTheme?: string;
  } = await req.json();

  const { text } = await generateText({
    model: groq('gemma2-9b-it'),
    system: `You are a Manim expert coder who is absolutely best at creating visually stunning animations. Create a clean, educational animation that explains the given concept step-by-step.
**OUTPUT:** Only Python code for a single Scene class. No explanations, markdown, or comments.

**USE ONLY:** Built-in Manim primitives:
- Text, MathTex (simple LaTeX only: "x = vt", "F = ma"), but use less latex and math related formulas as least as possible. Try to explain more based on visuals and animations in simplier terms.
- Circle, Square, Rectangle, Line, Arrow, Arc, Polygon
- NumberLine, Axes, BarChart, Table, Group, VGroup, or any necessary charts and graphs and animations
- FadeIn, FadeOut, Create, Uncreate, Write, Transform, Scale, Rotate, Shift

**NEVER USE:** SVGMobject, ImageMobject, VideoMobject, or external files

**ANIMATION FLOW:**
1. Start with animated title
2. Introduce elements step-by-step with Create/Write
3. Use shapes, colors, and motion to explain relationships
4. Clean removal (FadeOut/Uncreate) before new elements
5. End with key insight or summary

**STYLE:**
- Target 14-year-olds: simple, visual, slow-paced
- Clean layout, no overlapping elements
- Use color and movement to guide attention
- Minimal text, maximum visual explanation

Code must run perfectly on ManimCE ≥ 0.18.0 without modification.`,
    prompt: `Create a Manim scene for the following concept of ${mainTheme}:\n\nTitle: ${schema.title}\nDescription: ${schema.description}`,
  });

  const cleanCode = text.replace(/```python\n/, "").replace(/```/, "");

  return NextResponse.json({ code: cleanCode });
}
