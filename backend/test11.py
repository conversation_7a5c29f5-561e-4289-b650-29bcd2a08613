#!/usr/bin/env python3
"""
Comprehensive test script for ClarifAI Backend API
Tests all endpoints including render jobs, batch jobs, status monitoring, and queue management.
"""

import asyncio
import httpx
import json
import time
from typing import Optional, Dict, Any
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:8000"
TIMEOUT = 30.0

# Sample Manim scripts for testing
SIMPLE_SCRIPT = """
from manim import *

class TestScene(Scene):
    def construct(self):
        text = Text("Hello from ClarifAI!", font_size=48)
        self.play(Write(text))
        self.wait(1)
        self.play(FadeOut(text))
"""

COMPLEX_SCRIPT = """
from manim import *

class ComplexScene(Scene):
    def construct(self):
        # Create a circle and square
        circle = Circle(radius=1, color=BLUE)
        square = Square(side_length=2, color=RED)
        
        # Position them
        circle.shift(LEFT * 2)
        square.shift(RIGHT * 2)
        
        # Animate
        self.play(Create(circle), Create(square))
        self.wait(0.5)
        
        # Transform circle to square
        self.play(Transform(circle, square))
        self.wait(1)
        
        # Add text
        text = Text("Transformation Complete!", font_size=36)
        text.shift(UP * 2)
        self.play(Write(text))
        self.wait(1)
"""

MATH_SCRIPT = """
from manim import *

class MathScene(Scene):
    def construct(self):
        # Create mathematical equation
        equation = MathTex(r"e^{i\pi} + 1 = 0")
        equation.scale(2)
        
        self.play(Write(equation))
        self.wait(2)
        
        # Highlight parts
        self.play(equation.animate.set_color(YELLOW))
        self.wait(1)
"""

class BackendTester:
    """Comprehensive backend API tester."""
    
    def __init__(self, base_url: str = BASE_URL):
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=TIMEOUT)
        self.test_results = []
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()
    
    def log_test(self, test_name: str, success: bool, details: str = ""):
        """Log test result."""
        status = "✅ PASS" if success else "❌ FAIL"
        timestamp = datetime.now().strftime("%H:%M:%S")
        message = f"[{timestamp}] {status} {test_name}"
        if details:
            message += f" - {details}"
        print(message)
        
        self.test_results.append({
            "test": test_name,
            "success": success,
            "details": details,
            "timestamp": timestamp
        })
    
    async def test_health_check(self) -> bool:
        """Test basic health check."""
        try:
            response = await self.client.get(f"{self.base_url}/")
            response.raise_for_status()
            data = response.json()
            
            success = "message" in data
            self.log_test("Health Check", success, f"Status: {response.status_code}")
            return success
            
        except Exception as e:
            self.log_test("Health Check", False, str(e))
            return False
    
    async def test_single_render(self) -> Optional[str]:
        """Test single render job submission."""
        try:
            payload = {
                "script": SIMPLE_SCRIPT,
                "scene_name": "TestScene",
                "priority": 1
            }
            
            response = await self.client.post(
                f"{self.base_url}/render",
                json=payload
            )
            response.raise_for_status()
            data = response.json()
            
            success = "job_id" in data and "status" in data
            job_id = data.get("job_id") if success else None
            
            self.log_test(
                "Single Render Job", 
                success, 
                f"Job ID: {job_id}, Status: {data.get('status')}"
            )
            return job_id
            
        except Exception as e:
            self.log_test("Single Render Job", False, str(e))
            return None
    
    async def test_batch_render(self) -> Optional[str]:
        """Test batch render job submission."""
        try:
            payload = {
                "scripts": [
                    {"script": SIMPLE_SCRIPT, "scene_name": "TestScene"},
                    {"script": MATH_SCRIPT, "scene_name": "MathScene"},
                    {"script": COMPLEX_SCRIPT, "scene_name": "ComplexScene"}
                ],
                "priority": 2
            }
            
            response = await self.client.post(
                f"{self.base_url}/batch_render",
                json=payload
            )
            response.raise_for_status()
            data = response.json()
            
            success = "job_id" in data and "status" in data
            job_id = data.get("job_id") if success else None
            
            self.log_test(
                "Batch Render Job", 
                success, 
                f"Job ID: {job_id}, Scripts: {len(payload['scripts'])}"
            )
            return job_id
            
        except Exception as e:
            self.log_test("Batch Render Job", False, str(e))
            return None
    
    async def test_job_status(self, job_id: str) -> Dict[str, Any]:
        """Test job status retrieval."""
        try:
            response = await self.client.get(f"{self.base_url}/jobs/{job_id}")
            response.raise_for_status()
            data = response.json()
            
            required_fields = ["job_id", "job_type", "status", "created_at"]
            success = all(field in data for field in required_fields)
            
            self.log_test(
                f"Job Status ({job_id[:8]}...)", 
                success, 
                f"Status: {data.get('status')}, Type: {data.get('job_type')}"
            )
            return data
            
        except Exception as e:
            self.log_test(f"Job Status ({job_id[:8]}...)", False, str(e))
            return {}
    
    async def test_list_jobs(self) -> bool:
        """Test job listing endpoint."""
        try:
            # Test basic listing
            response = await self.client.get(f"{self.base_url}/jobs")
            response.raise_for_status()
            data = response.json()
            
            success = isinstance(data, dict) and "jobs" in data
            job_count = len(data.get("jobs", [])) if success else 0
            
            self.log_test("List Jobs", success, f"Found {job_count} jobs")
            
            # Test with filters
            if success:
                await self._test_job_filters()
            
            return success
            
        except Exception as e:
            self.log_test("List Jobs", False, str(e))
            return False
    
    async def _test_job_filters(self):
        """Test job listing with various filters."""
        filters = [
            {"status": "queued"},
            {"status": "processing"},
            {"status": "completed"},
            {"job_type": "render"},
            {"job_type": "batch_render"},
            {"limit": 5}
        ]
        
        for filter_params in filters:
            try:
                response = await self.client.get(
                    f"{self.base_url}/jobs",
                    params=filter_params
                )
                response.raise_for_status()
                data = response.json()
                
                filter_str = ", ".join(f"{k}={v}" for k, v in filter_params.items())
                job_count = len(data.get("jobs", []))
                
                self.log_test(
                    f"List Jobs Filter ({filter_str})", 
                    True, 
                    f"Found {job_count} jobs"
                )
                
            except Exception as e:
                filter_str = ", ".join(f"{k}={v}" for k, v in filter_params.items())
                self.log_test(f"List Jobs Filter ({filter_str})", False, str(e))
    
    async def test_queue_stats(self) -> bool:
        """Test queue statistics endpoint."""
        try:
            response = await self.client.get(f"{self.base_url}/queues/stats")
            response.raise_for_status()
            data = response.json()
            
            success = "queues" in data and "total_active_jobs" in data
            
            if success:
                queues = data.get("queues", {})
                active_jobs = data.get("total_active_jobs", 0)
                queue_names = list(queues.keys())
                
                self.log_test(
                    "Queue Statistics", 
                    success, 
                    f"Queues: {queue_names}, Active: {active_jobs}"
                )
            else:
                self.log_test("Queue Statistics", success, "Missing required fields")
            
            return success
            
        except Exception as e:
            self.log_test("Queue Statistics", False, str(e))
            return False
    
    async def monitor_job(self, job_id: str, max_checks: int = 10, interval: int = 3):
        """Monitor a job until completion or timeout."""
        print(f"\n🔍 Monitoring job {job_id[:8]}...")
        
        for i in range(max_checks):
            status_data = await self.test_job_status(job_id)
            
            if not status_data:
                break
            
            status = status_data.get("status", "unknown")
            progress = status_data.get("progress_percentage")
            
            print(f"   Check {i+1}/{max_checks}: Status = {status}", end="")
            if progress is not None:
                print(f", Progress = {progress}%", end="")
            print()
            
            # Check if job is complete
            if status in ["completed", "failed", "cancelled"]:
                if status == "completed":
                    result = status_data.get("result", {})
                    output_urls = result.get("output_urls", [])
                    processing_time = result.get("processing_time_seconds", 0)
                    
                    print(f"   ✅ Job completed in {processing_time}s")
                    if output_urls:
                        print(f"   📹 Output: {output_urls[0]}")
                else:
                    error_msg = status_data.get("error_message", "Unknown error")
                    print(f"   ❌ Job {status}: {error_msg}")
                break
            
            if i < max_checks - 1:
                await asyncio.sleep(interval)
        else:
            print(f"   ⚠️  Monitoring timeout after {max_checks} checks")
    
    async def test_invalid_requests(self):
        """Test error handling with invalid requests."""
        test_cases = [
            {
                "name": "Invalid Script Syntax",
                "endpoint": "/render",
                "payload": {"script": "invalid python code", "scene_name": "Test"}
            },
            {
                "name": "Missing Script",
                "endpoint": "/render", 
                "payload": {"scene_name": "Test"}
            },
            {
                "name": "Invalid Job ID",
                "endpoint": "/jobs/invalid-job-id",
                "method": "GET"
            },
            {
                "name": "Invalid Priority",
                "endpoint": "/render",
                "payload": {"script": SIMPLE_SCRIPT, "priority": 999}
            }
        ]
        
        for case in test_cases:
            try:
                if case.get("method") == "GET":
                    response = await self.client.get(f"{self.base_url}{case['endpoint']}")
                else:
                    response = await self.client.post(
                        f"{self.base_url}{case['endpoint']}", 
                        json=case.get("payload", {})
                    )
                
                # We expect these to fail (4xx or 5xx status codes)
                success = 400 <= response.status_code < 600
                
                self.log_test(
                    f"Error Handling: {case['name']}", 
                    success, 
                    f"Status: {response.status_code}"
                )
                
            except Exception as e:
                # Exceptions are also acceptable for invalid requests
                self.log_test(f"Error Handling: {case['name']}", True, f"Exception: {str(e)[:50]}")
    
    def print_summary(self):
        """Print test summary."""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"\n{'='*60}")
        print(f"TEST SUMMARY")
        print(f"{'='*60}")
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests} ✅")
        print(f"Failed: {failed_tests} ❌")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%" if total_tests > 0 else "N/A")
        
        if failed_tests > 0:
            print(f"\nFailed Tests:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  - {result['test']}: {result['details']}")

async def main():
    """Main test execution."""
    print("🚀 Starting ClarifAI Backend API Tests")
    print(f"Server URL: {BASE_URL}")
    print(f"Timeout: {TIMEOUT}s")
    print("="*60)
    
    async with BackendTester(BASE_URL) as tester:
        # Basic health check
        if not await tester.test_health_check():
            print("❌ Server is not responding. Please check if the server is running.")
            return
        
        # Test job submission
        print("\n📝 Testing Job Submission...")
        single_job_id = await tester.test_single_render()
        batch_job_id = await tester.test_batch_render()
        
        # Test job monitoring
        print("\n📊 Testing Job Monitoring...")
        await tester.test_list_jobs()
        await tester.test_queue_stats()
        
        # Monitor submitted jobs
        if single_job_id:
            await tester.monitor_job(single_job_id, max_checks=5, interval=2)
        
        if batch_job_id:
            await tester.monitor_job(batch_job_id, max_checks=5, interval=2)
        
        # Test error handling
        print("\n🚨 Testing Error Handling...")
        await tester.test_invalid_requests()
        
        # Print summary
        tester.print_summary()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⚠️  Tests interrupted by user")
    except Exception as e:
        print(f"\n❌ Test execution failed: {e}")
