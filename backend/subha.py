import os
import subprocess
import shutil
import requests
import sys
import uuid
from dotenv import load_dotenv

try:
    import google.generativeai as genai
except ModuleNotFoundError:
    print("Error: Required package 'google-generativeai' not installed.")
    print("Please run: pip install google-generativeai")
    sys.exit(1)

# Load environment variables
load_dotenv()

# Initialize Gemini
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
if not GEMINI_API_KEY:
    print("Error: GEMINI_API_KEY not found in .env file")
    sys.exit(1)

try:
    genai.configure(api_key=GEMINI_API_KEY)
    model = genai.GenerativeModel('gemini-2.0-flash-lite')
except Exception as e:
    print(f"Error initializing Gemini: {e}")
    sys.exit(1)

def generate_lecture_text(description):
    prompt = f"""
You are a firm, no-nonsense university professor.
You speak with calm authority and complete control.
Your tone is disciplined, focused, and unwavering.
You are addressing a lecture hall. You are not here to entertain — you are here to teach.
Deliver the concept of {description} clearly and concisely, in your natural, authoritative teaching voice.
Keep it brief, direct, and under 200 words.
Nothing more. Nothing less.
"""
    try:
        response = model.generate_content(prompt)
        return response.text
    except Exception as e:
        print(f"Error generating lecture text: {e}")
        return None

def generate_text_to_speech(text, output_filename):
    url = "https://cf-tts.xeven.workers.dev/tts"
    headers = {"Content-Type": "application/json"}
    
    os.makedirs("media/audio", exist_ok=True)
    output_path = os.path.join("media/audio", output_filename)
    
    try:
        response = requests.post(url, json={"text": text}, headers=headers)
        response.raise_for_status()
        
        with open(output_path, "wb") as f:
            f.write(response.content)
            
        print(f"🔊 Audio saved to {output_path}")
        return output_path
    except requests.exceptions.RequestException as e:
        print(f"Error generating speech: {e}")
        return None

def merge_video_audio(video_id, audio_path):
    audio_id = os.path.splitext(os.path.basename(audio_path))[0]
    video_folder = f"media/{video_id}"
    video_path = os.path.join(video_folder, "NeuralNetworksExplanation.mp4")
    stretched_audio_path = f"media/audio/{audio_id}_slow.aac"
    output_path = f"media/result/{video_id}_{audio_id}_final.mp4"

    os.makedirs("media/result", exist_ok=True)

    # Tempo adjustment
    atempo_chain = []
    tempo = 0.7
    while tempo < 0.5:
        atempo_chain.append("atempo=0.5")
        tempo /= 0.5
    atempo_chain.append(f"atempo={tempo:.5f}")
    atempo_filter = ",".join(atempo_chain)

    print(f"🐢 Applying tempo: {atempo_filter}")

    # Step 1: Slow down audio
    stretch_command = [
        "ffmpeg", "-y",
        "-i", audio_path,
        "-filter:a", atempo_filter,
        "-vn", "-acodec", "aac",
        stretched_audio_path
    ]
    subprocess.run(stretch_command, check=True)

    # Step 2: Merge with video
    merge_command = [
        "ffmpeg", "-y",
        "-i", video_path,
        "-i", stretched_audio_path,
        "-map", "0:v:0",
        "-map", "1:a:0",
        "-c:v", "copy",
        "-c:a", "aac",
        "-shortest",
        output_path
    ]
    subprocess.run(merge_command, check=True)
    print(f"✅ Final video with slowed audio saved at: {output_path}")

    # Cleanup
    if os.path.isdir(video_folder):
        shutil.rmtree(video_folder)
        print(f"🗑️ Deleted video folder: {video_folder}")
    if os.path.isfile(audio_path):
        os.remove(audio_path)
        print(f"🗑️ Deleted original audio: {audio_path}")
    if os.path.isfile(stretched_audio_path):
        os.remove(stretched_audio_path)
        print(f"🧹 Deleted stretched audio: {stretched_audio_path}")

def generate_and_merge(video_id, description):
    audio_id = str(uuid.uuid4())[:8] + "_lecture"
    lecture_text = generate_lecture_text(description)
    
    if not lecture_text:
        return

    audio_path = generate_text_to_speech(lecture_text, f"{audio_id}.mp3")
    
    if not audio_path:
        return

    print("\n📖 Generated Lecture Text:\n")
    print(lecture_text)

    merge_video_audio(video_id, audio_path)

# Example usage
if __name__ == "__main__":
    video_id = "f063958f42534458b94b93ec688face4"
    topic_description = "Animated title 'Neural Networks in Machine Learning' appears with a visual hook of a glowing brain made of interconnected neurons. The animation transitions to a simple question"
    generate_and_merge(video_id, topic_description)
