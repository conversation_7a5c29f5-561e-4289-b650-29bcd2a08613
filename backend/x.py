import os
import subprocess
import tempfile
import requests
import sys
import uuid
import re
import shutil

# === CONFIG ===
GEMINI_API_KEY = "AIzaSyB6gaoN8ZDl-JSp6e-1JJ6VmfUcXSwMKlA"
TTS_ENDPOINT = "https://cf-tts.xeven.workers.dev/tts"
RESULT_DIR = "media/result"
AUDIO_DIR = "media/audio"
VIDEO_DIR = "media/videos"

# === INIT GEMINI ===
try:
    import google.generativeai as genai
    genai.configure(api_key=GEMINI_API_KEY)
    model = genai.GenerativeModel("gemini-1.5-flash")
except Exception as e:
    print("Gemini init failed:", e)
    sys.exit(1)


# === 1. NARRATION GENERATION ===
def generate_narration(manim_code: str) -> str:
    prompt = f"""
You are a professional educational narrator.
Describe the concepts being illustrated, the animations, and the mathematical ideas being conveyed from the Manim code.
You speak with calm authority and complete control.
Your tone is disciplined, focused, and unwavering.
You are addressing a lecture hall. You are not here to entertain — you are here to teach.
Understand what the animations are doing and explain them clearly and accurately.
Give under 250 words of narration.

Generate a very concise, authoritative narration for the following Manim code:

{manim_code}
"""
    try:
        response = model.generate_content(prompt)
        narration_text = response.text.strip()
        return narration_text
    except Exception as e:
        print("Narration generation failed:", e)
        return ""


# === 2. TTS GENERATION ===
def generate_tts_audio(text: str):
    headers = {"Content-Type": "application/json"}
    os.makedirs(AUDIO_DIR, exist_ok=True)
    temp_audio = tempfile.NamedTemporaryFile(suffix=".mp3", dir=AUDIO_DIR, delete=False)
    try:
        response = requests.post(TTS_ENDPOINT, headers=headers, json={"text": text})
        response.raise_for_status()
        temp_audio.write(response.content)
        temp_audio.flush()
        print("🔊 TTS audio ready")
        return temp_audio.name
    except Exception as e:
        print("TTS generation failed:", e)
        return None


# === 3. MANIM RENDER ===
def render_manim_video(code_str: str, class_name: str, video_id: str):
    py_file = f"{video_id}.py"
    with open(py_file, "w", encoding="utf-8") as f:
        f.write(code_str)

    print(f"🆔 Rendering with video ID: {video_id}")

    try:
        subprocess.run(
            ["manim", "-qk", "-o", f"{video_id}.mp4", py_file, class_name, "--media_dir", "media"],
            capture_output=True,
            text=True,
            check=True,
        )
    except subprocess.CalledProcessError as e:
        print("🚫 Manim render failed!")
        print(e.stdout or "")
        print(e.stderr or "")
        if os.path.exists(py_file):
            os.remove(py_file)
        return None, None

    # Recursively search for the video
    for root, _, files in os.walk(VIDEO_DIR):
        for file in files:
            if file == f"{video_id}.mp4":
                full_path = os.path.join(root, file)
                print(f"🎞️ Video found at: {full_path}")
                return full_path, py_file

    raise FileNotFoundError("❌ Could not find generated video in media/videos")


# === 4. MERGE VIDEO + SLOWED AUDIO ===
def merge_audio_video(video_path: str, audio_path: str, video_id: str):
    os.makedirs(RESULT_DIR, exist_ok=True)
    stretched_audio = os.path.join(AUDIO_DIR, f"{video_id}_slow.aac")
    final_output = os.path.join(RESULT_DIR, f"{video_id}_final.mp4")

    subprocess.run([
        "ffmpeg", "-y", "-i", audio_path,
        "-filter:a", "atempo=0.7",
        "-vn", "-acodec", "aac", stretched_audio
    ], check=True)

    subprocess.run([
        "ffmpeg", "-y", "-i", video_path, "-i", stretched_audio,
        "-map", "0:v:0", "-map", "1:a:0",
        "-c:v", "copy", "-c:a", "aac", "-shortest",
        final_output
    ], check=True)

    print(f"✅ Final video saved at: {final_output}")

    if os.path.exists(audio_path):
        os.remove(audio_path)
    if os.path.exists(stretched_audio):
        os.remove(stretched_audio)

    return final_output


# === 5. MAIN WRAPPER ===
def manim_to_final_video(code_str: str, class_name: str = "AutoScene"):
    video_id = str(uuid.uuid4())[:8]
    print(f"🔍 Generated Video ID: {video_id}")

    narration = generate_narration(code_str)
    if not narration:
        print("❌ Narration is empty")
        return

    print("\n📢 Narration:\n", narration)

    audio_path = generate_tts_audio(narration)
    if not audio_path:
        return

    video_path, py_file = render_manim_video(code_str, class_name, video_id)
    if not video_path:
        return

    final_path = merge_audio_video(video_path, audio_path, video_id)

    video_folder = os.path.join("media", "videos", video_id)
    if os.path.exists(video_folder):
        shutil.rmtree(video_folder)
        print(f"🗑️ Deleted video folder: {video_folder}")
    if os.path.exists(py_file):
        os.remove(py_file)
        print(f"🗑️ Deleted temp py: {py_file}")

    print(f"🎉 All done. Final video at: {final_path}")


# ✅ Sample usage
manim_code = """
class PlotExample(Scene):
    def construct(self):
        plot_axes = Axes(
            x_range=[0, 1, 0.05],
            y_range=[0, 1, 0.05],
            x_length=9,
            y_length=5.5,
            axis_config={
                "numbers_to_include": np.arange(0, 1 + 0.1, 0.1),
                "font_size": 24,
            },
            tips=False,
        )

        y_label = plot_axes.get_y_axis_label("y", edge=LEFT, direction=LEFT, buff=0.4)
        x_label = plot_axes.get_x_axis_label("x")
        plot_labels = VGroup(x_label, y_label)

        plots = VGroup()
        for n in np.arange(1, 20 + 0.5, 0.5):
            plots += plot_axes.plot(lambda x: x**n, color=WHITE)
            plots += plot_axes.plot(
                lambda x: x**(1 / n), color=WHITE, use_smoothing=False
            )

        extras = VGroup()
        extras += plot_axes.get_horizontal_line(plot_axes.c2p(1, 1, 0), color=BLUE)
        extras += plot_axes.get_vertical_line(plot_axes.c2p(1, 1, 0), color=BLUE)
        extras += Dot(point=plot_axes.c2p(1, 1, 0), color=YELLOW)
        title = Title(
            r"Graphs of $y=x^{\frac{1}{n}}$ and $y=x^n (n=1, 1.5, 2, 2.5, 3, \dots, 20)$",
            include_underline=False,
            font_size=40,
        )
        
        self.play(Write(title))
        self.play(Create(plot_axes), Create(plot_labels), Create(extras))
        self.play(AnimationGroup(*[Create(plot) for plot in plots], lag_ratio=0.05))
"""

if __name__ == "__main__":
    manim_to_final_video(manim_code)
