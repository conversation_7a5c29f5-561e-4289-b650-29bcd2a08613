import requests

API_URL = "http://127.0.0.1:8000/render"

payload = {
    "script": """
from manim import *

class SimpleHarmonicMotion(Scene):
    def construct(self):
        title = Text("Simple Harmonic Motion", font_size=48)
        self.play(Write(title))
        self.wait(2)
        self.play(FadeOut(title))

        # Pendulum setup
        pendulum_rod = Line(start=UP * 2, end=ORIGIN, color=GRAY)
        pendulum_bob = Circle(radius=0.3, color=BLUE, fill_opacity=1)
        pendulum = Group(pendulum_rod, pendulum_bob)
        self.play(Create(pendulum_rod), Create(pendulum_bob))
        self.wait(1)

        # Pendulum swing
        self.play(
            pendulum.animate.shift(LEFT * 2).rotate(0.5),
            run_time=1.5
        )
        self.wait(0.5)
        self.play(
            pendulum.animate.shift(RIGHT * 4).rotate(-1),
            run_time=3
        )
        self.wait(0.5)
        self.play(
            pendulum.animate.shift(LEFT * 2).rotate(0.5),
            run_time=1.5
        )
        self.wait(1)
        self.play(FadeOut(pendulum))

        # Spring setup
        spring = VGroup(*[Line(start=LEFT * 0.5 + UP * i * 0.2, end=LEFT * 0.5 + UP * (i + 1) * 0.2 + RIGHT * 0.2, color=YELLOW, stroke_width=4) for i in range(10)])
        spring.move_to(UP * 2)
        mass = Square(side_length=0.6, color=RED, fill_opacity=1).shift(DOWN * 2)
        self.play(Create(spring), Create(mass))
        self.wait(1)

        # Spring oscillation
        self.play(
            mass.animate.shift(RIGHT * 1.5),
            spring.animate.shift(RIGHT * 1.5),
            run_time=1
        )
        self.wait(0.5)
        self.play(
            mass.animate.shift(LEFT * 3),
            spring.animate.shift(LEFT * 3),
            run_time=2
        )
        self.wait(0.5)
        self.play(
            mass.animate.shift(RIGHT * 1.5),
            spring.animate.shift(RIGHT * 1.5),
            run_time=1
        )
        self.wait(1)
        self.play(FadeOut(spring), FadeOut(mass))

        # Summary
        summary_text = Text("Motion that repeats", font_size=36)
        self.play(Write(summary_text))
        self.wait(2)
        self.play(FadeOut(summary_text))
"""
}

response = requests.post(API_URL, json=payload)

print("Status Code:", response.status_code)
try:
    print("Response:", response.json())
except Exception:
    print("Failed to parse JSON. Raw response:", response.text)
